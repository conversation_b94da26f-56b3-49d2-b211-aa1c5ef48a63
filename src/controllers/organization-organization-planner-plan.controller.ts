import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  api,
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {
  Organization,
  OrganizationPlannerPlan,
} from '../models';
import {OrganizationPlannerPlanRepository, OrganizationRepository, ConversationRepository, UserRepository} from '../repositories';
import {modelForGuard, modelIdForGuard, OrgGuardPropertyStrategy, restrictReadsWithGuard, skipGuardCheck} from '../interceptors/crud-guard.interceptor';
import {injectGuardedFilter, injectUserOrgId, guardStrategy} from '../interceptors/crud-guard.interceptor';
import {authenticate} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {basicAuthorization} from '../services/basic.authorizor';

@api({basePath: '/api/v1'})
@guardStrategy(new OrgGuardPropertyStrategy<Organization>({
  orgIdModelPropertyName: 'id',
  repositoryClass: OrganizationRepository
}))
export class OrganizationOrganizationPlannerPlanController {
  constructor(
    @repository(OrganizationRepository) protected organizationRepository: OrganizationRepository,
    @repository(ConversationRepository) protected conversationRepository: ConversationRepository,
    @repository(UserRepository) protected userRepository: UserRepository,
  ) { }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/organizations/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Array of Organization has many OrganizationPlannerPlan',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(OrganizationPlannerPlan)},
          },
        },
      },
    },
  })
  async find(
    @param.query.object('filter') filter?: Filter<OrganizationPlannerPlan>,
    @injectUserOrgId() orgId?: number
  ): Promise<OrganizationPlannerPlan[]> {
    // Prepare filter object with default ordering and includes
    const finalFilter: Filter<OrganizationPlannerPlan> = {
      order: ['startdate ASC'],
      include: [
        {
          relation: 'plannerPlanVersions',
          scope: {
            include: [{
              relation: 'plannerCampaigns',
              scope: {
                where: { or: [{ isDeleted: false }, { isDeleted: { eq: null } }] },
                include: [{
                  relation: 'task'
                }]
              }
            }]
          }
        }
      ],
    };

    // If a filter was provided, merge it with our defaults
    if (filter) {
      // If caller provided 'where' clause, merge it with our defaults
      if (filter.where) {
        finalFilter.where = filter.where;
      }

      // If caller provided custom 'order', use that instead of default
      if (filter.order) {
        finalFilter.order = filter.order;
      }

      // Preserve other filter properties (like 'limit', 'skip', etc.)
      if (filter.limit) finalFilter.limit = filter.limit;
      if (filter.skip) finalFilter.skip = filter.skip;
      if (filter.fields) finalFilter.fields = filter.fields;
    }

    const plans = await this.organizationRepository.organizationPlannerPlans(orgId).find(finalFilter);

    // Add creator information to each plan
    for (const plan of plans) {
      // Find the first campaign in the plan to get creator information
      const firstCampaign = plan.plannerPlanVersions?.[0]?.plannerCampaigns?.[0];

      // Try multiple sources for conversation ID: campaign's originalChatId/chatId, or task's conversation IDs
      let conversationId = null;
      if (firstCampaign?.originalChatId) {
        conversationId = firstCampaign.originalChatId;
      } else if (firstCampaign?.chatId) {
        conversationId = firstCampaign.chatId;
      } else if (firstCampaign?.task?.originalConversationId) {
        conversationId = firstCampaign.task.originalConversationId;
      } else if (firstCampaign?.task?.conversationId) {
        conversationId = firstCampaign.task.conversationId;
      }

      if (conversationId) {
        try {
          const conversation = await this.conversationRepository.findById(conversationId, {
            include: [{relation: 'createdByUser'}]
          });

          if (conversation?.createdByUser) {
            (plan as any).createdBy = {
              id: conversation.createdByUser.id,
              firstName: conversation.createdByUser.firstName,
              lastName: conversation.createdByUser.lastName,
              email: conversation.createdByUser.email,
            };
            (plan as any).createdByName = `${conversation.createdByUser.firstName || ''} ${conversation.createdByUser.lastName || ''}`.trim() || conversation.createdByUser.email;
          }
        } catch (error) {
          console.error(`Error fetching creator for plan ${plan.id}:`, error);
          // Continue without creator info if there's an error
        }
      } else {
        console.log(`No conversation ID found for plan ${plan.id}, campaign:`, {
          campaignId: firstCampaign?.id,
          originalChatId: firstCampaign?.originalChatId,
          chatId: firstCampaign?.chatId,
          taskOriginalConversationId: firstCampaign?.task?.originalConversationId,
          taskConversationId: firstCampaign?.task?.conversationId
        });
      }
    }

    return plans;
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @skipGuardCheck()
  @get('/organizations/organization-planner-plans/{id}', {
    responses: {
      '200': {
        description: 'Array of Organization has many OrganizationPlannerPlan',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(OrganizationPlannerPlan)},
          },
        },
      },
    },
  })
  async findOne(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<OrganizationPlannerPlan>,
    @injectUserOrgId() orgId?: number
  ): Promise<OrganizationPlannerPlan[]> {
    // Prepare filter object with default where and includes
    const finalFilter: Filter<OrganizationPlannerPlan> = {
      where: {id: id},
      include: [
        {
          relation: 'plannerPlanVersions',
          scope: {
            include: [{
              relation: 'plannerCampaigns',
              scope: {
                where: { or: [{ isDeleted: false }, { isDeleted: { eq: null } }] },
                include: [{
                  relation: 'task'
                }]
              }
            }]
          }
        }
      ],
    };

    // If a filter was provided, merge it with our defaults
    if (filter) {
      // If caller provided 'where' clause, merge it with our id filter
      if (filter.where) {
        finalFilter.where = {and: [
          {id: id},
          filter.where
        ]};
      }

      // Preserve other filter properties (like 'limit', 'skip', etc.)
      if (filter.order) finalFilter.order = filter.order;
      if (filter.limit) finalFilter.limit = filter.limit;
      if (filter.skip) finalFilter.skip = filter.skip;
      if (filter.fields) finalFilter.fields = filter.fields;
    }

    return this.organizationRepository.organizationPlannerPlans(orgId).find(finalFilter);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @post('/organizations/{id}/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Organization model instance',
        content: {'application/json': {schema: getModelSchemaRef(OrganizationPlannerPlan)}},
      },
    },
  })
  async create(
    @modelIdForGuard(Organization)
    @param.path.number('id') id: typeof Organization.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OrganizationPlannerPlan, {
            title: 'NewOrganizationPlannerPlanInOrganization',
            exclude: ['id'],
            optional: ['organizationId']
          }),
        },
      },
    }) organizationPlannerPlan: Omit<OrganizationPlannerPlan, 'id'>,
  ): Promise<OrganizationPlannerPlan> {
    // Set the creation date
    organizationPlannerPlan.createdDate = new Date();
    return this.organizationRepository.organizationPlannerPlans(id).create(organizationPlannerPlan);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @patch('/organizations/{id}/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Organization.OrganizationPlannerPlan PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @modelIdForGuard(Organization)
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(OrganizationPlannerPlan, {partial: true}),
        },
      },
    })
    organizationPlannerPlan: Partial<OrganizationPlannerPlan>,
    @param.query.object('where', getWhereSchemaFor(OrganizationPlannerPlan)) where?: Where<OrganizationPlannerPlan>,
  ): Promise<Count> {
    return this.organizationRepository.organizationPlannerPlans(id).patch(organizationPlannerPlan, where);
  }

  @authenticate('jwt')
  @authorize({
    allowedRoles: ['admin', 'support', 'customer'],
    voters: [basicAuthorization],
  })
  @del('/organizations/{id}/organization-planner-plans', {
    responses: {
      '200': {
        description: 'Organization.OrganizationPlannerPlan DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @modelIdForGuard(Organization)
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(OrganizationPlannerPlan)) where?: Where<OrganizationPlannerPlan>,
  ): Promise<Count> {
    return this.organizationRepository.organizationPlannerPlans(id).delete(where);
  }
}
